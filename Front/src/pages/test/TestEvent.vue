<script>
import VueJsonPretty from 'vue-json-pretty'
import FilterModelUtil from '@/components/wolf-static-cpnt/event/models/FilterModelUtil'
import { CPNTEventFilter } from '@/components/wolf-static-cpnt/index'

import {
  demoValue,
  mockEventDataProvider,
} from './TestEventConfig'

import 'vue-json-pretty/lib/styles.css'

export default {
  name: 'TestEvent',
  components: {
    CPNTEventFilter,
    VueJsonPretty,
  },
  data() {
    return {
      jsonData: '{}',
      filterValue: {},
      filterValue2: {}, // 第二个Filter组件的值
      mode: 'edit',
      mockDataProvider: mockEventDataProvider,
    }
  },
  watch: {
    // filterValue: {
    //   handler(newVal) {
    //     // eslint-disable-next-line no-console
    //     console.log('filterValue changed:', newVal)
    //   },
    // },
  },
  mounted() {
    // 初始化一个空的过滤器
    this.filterValue = FilterModelUtil.initCreateFilterGroup(true, false)
    // this.filterValue2 = FilterModelUtil.initCreateFilterGroup(true, false)

    window.setFilterValue = this.setDemoValue
  },
  methods: {
    getEventTypeName(eventType) {
      const typeMap = {
        BURIED_POINT_EVENT: '埋点事件',
        CUSTOM_EVENT: '自定义事件',
        SYSTEM_EVENT: '系统事件',
        OTHER: '其他事件',
      }
      return typeMap[eventType] || eventType
    },

    handleFilterChange(validJson, _fullValue) {
      this.filterValue = validJson || {}
    },

    handleFilterChange2(validJson, _fullValue) {
      this.filterValue2 = validJson || {}
    },

    // 获取第一个Filter组件的firstAction
    getFirstFilterFirstAction() {
      return FilterModelUtil.getFirstFilterFirstAction(this.filterValue)
    },

    toggleMode() {
      this.mode = this.mode === 'edit' ? 'detail' : 'edit'
    },

    clearFilter() {
      this.filterValue = {}
      this.filterValue2 = {}
    },

    submitJsonData() {
      // 设置value
      this.filterValue = JSON.parse(this.jsonData)
    },

    setDemoValue() {
      this.filterValue = demoValue
    },

    copyJson() {
      this.$copyText(JSON.stringify(this.filterValue, null, 2))
    },

    validateFilter() {
      if (this.$refs.eventFilter) {
        const isValid = this.$refs.eventFilter.isValid(true)
        if (isValid) {
          this.$message.success('过滤器校验通过')
        }
        else {
          window.console.log(isValid, '🚀 ~ isValid:')
          this.$message.error('过滤器校验失败，请检查配置')
        }
        return isValid
      }
      this.$message.warning('过滤器组件未找到')
      return false
    },
  },
}
</script>

<template>
  <div class="test-event-page">
    <h1>Event Filter 测试页面</h1>
    <div class="test-section">
      <h3>控制面板:</h3>
      <a-button @click="toggleMode">
        切换模式 (当前: {{ mode }})
      </a-button>
      <a-button style="margin-left: 8px" @click="clearFilter">
        清空过滤器
      </a-button>
    </div>

    <div class="flex">
      <div class="w-[85%]">
        <div class="test-section bg-[#fff]">
          <h2>第一个 Event Filter (主Filter)</h2>
          <CPNTEventFilter
            ref="eventFilter"
            :value="filterValue"
            :data-provider="mockDataProvider"
            :on-change="handleFilterChange"
            :mode="mode"
            :show-init-line="true"
            :is-action-collection="false"
          />
        </div>

        <div class="test-section bg-[#f9f9f9]">
          <h2>第二个 Event Filter (联动Filter)</h2>
          <p style="color: #666; margin-bottom: 16px;">
            这个Filter会使用第一个Filter的firstAction作为新增过滤器的默认值
            <br>
            当前第一个Filter的firstAction: <strong>{{ getFirstFilterFirstAction() || '无' }}</strong>
          </p>
          <CPNTEventFilter
            ref="eventFilter2"
            :value="filterValue2"
            :data-provider="mockDataProvider"
            :on-change="handleFilterChange2"
            :mode="mode"
            :show-init-line="true"
            :is-action-collection="false"
            :external-first-action="getFirstFilterFirstAction()"
          />
        </div>

        <div class="test-section flex gap-24">
          <div>
            <h3>当前过滤器值:</h3>
            <div class="mb-2">
              <a-button @click="copyJson">
                复制第一个Filter的json
              </a-button>
              <a-button style="margin-left: 8px" @click="validateFilter">
                校验第一个过滤器
              </a-button>
            </div>
            <h4>第一Filter的值:</h4>
            <VueJsonPretty :data="filterValue" />
          </div>

          <div>
            <h4 style="mb-20">
              第二个Filter的值:
            </h4>
            <VueJsonPretty :data="filterValue2" />
          </div>
        </div>
      </div>

      <div class="w-[15%]">
        <!-- 右侧JSON编辑区域 -->
        <div class="json-editor">
          <a-button style="margin-bottom: 10px" @click="submitJsonData">
            应用JSON数据
          </a-button>
          <a-button style="margin-bottom: 10px" @click="setDemoValue">
            设置demo数据
          </a-button>
          <a-textarea v-model="jsonData" :rows="30" placeholder="在此编辑JSON数据..." />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.test-event-page {
  padding: 20px;
  /* max-width: 1200px; */
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.test-section h2,
.test-section h3 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
