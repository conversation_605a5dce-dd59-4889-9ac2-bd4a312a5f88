import moment from 'moment'
import { TIME_STATE_MAP, TIME_TERMS } from './constants'

/**
 * 格式化时间字符串
 * @param {object} obj - 时间对象
 * @param {boolean} showTime - 是否显示具体时间
 * @returns {string} 格式化后的时间字符串
 */
export function formatTimeString(obj, showTime = false) {
  let str = ''
  if (typeof obj === 'object') {
    if (obj.type === 'ABSOLUTE') {
      const format = showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'
      str = moment(obj.timestamp).format(format)
    }
    else if (obj.type === 'RELATIVE') {
      const timeTerm = TIME_TERMS.find(n => n.value === obj.timeTerm)
      const timeState = TIME_STATE_MAP[JSON.stringify(obj.isPast)]
      str = `${obj.times}${timeTerm?.label}${timeState}`
    }
    else if (obj.type === 'NOW') {
      str = '现在'
    }
  }
  return str
}

/**
 * 生成事件时间显示文本
 * @param {object} value - FilterModel 实例
 * @param {object} FILTER_CONFIG - 配置对象
 * @returns {string} 显示文本
 */
export function generateEventTimeDisplayText(value, FILTER_CONFIG) {
  const { firstAction, firstTimeValue, firstTimeUnit } = value

  // 对于"先做过, 后未做过"模式，显示固定文本
  if (firstAction === 'FIRST_DO_LAST_NOT_DO') {
    return FILTER_CONFIG.FIRST_ACTION[firstAction] || ''
  }

  if (firstAction && firstTimeValue && firstTimeUnit) {
    const actionText = FILTER_CONFIG.FIRST_ACTION[firstAction] || ''
    const unitText = FILTER_CONFIG.TIME_TYPE[firstTimeUnit] || ''
    return `${actionText} 在${firstTimeValue}${unitText}之内`
  }
  return '请设置时间条件'
}

/**
 * 生成最后事件时间显示文本
 * @param {object} value - FilterModel 实例
 * @param {object} FILTER_CONFIG - 配置对象
 * @returns {string} 显示文本
 */
export function generateLastEventTimeDisplayText(value, FILTER_CONFIG) {
  const { lastTimeValue, lastTimeUnit } = value
  if (lastTimeValue && lastTimeUnit) {
    const unitText = FILTER_CONFIG.TIME_TYPE[lastTimeUnit] || ''
    return `并在接下来的${lastTimeValue}${unitText}之内`
  }
  return '请设置时间条件'
}

/**
 * 检查验证错误
 * @param {object} validator - 验证器对象
 * @returns {boolean} 是否有验证错误
 */
export function hasValidationError(validator) {
  return !!(
    validator?.action
    || validator?.id
    || validator?.eventAggregateProperty
    || validator?.fun
    || validator?.operator
    || validator?.value
    || validator?.firstAction
    || validator?.lastEventId
    || validator?.lastTimeValue
    || validator?.lastTimeUnit
    || validator?.lastAction
    || validator?.lastEventInfo
    || validator?.lastEventFilterProperty
  )
}

/**
 * 获取验证消息
 * @param {object} validator - 验证器对象
 * @returns {string} 验证消息
 */
export function getValidationMessage(validator) {
  const _ = require('lodash')
  return _.head(_.values(validator.message)) || ''
}

/**
 * 获取函数显示值
 * @param {object} eventAggregateProperty - 事件聚合属性
 * @param {object} FILTER_CONFIG - 配置对象
 * @returns {string} 函数显示值
 */
export function getFunValue(eventAggregateProperty, FILTER_CONFIG) {
  const _ = require('lodash')
  const { fun } = eventAggregateProperty
  if (fun) {
    return (
      _.find(FILTER_CONFIG.CONDITIONFUN[eventAggregateProperty?.property?.fieldType || 'INT'], (item) => {
        return item.value === fun
      }) || {}
    ).name
  }
  return ''
}
