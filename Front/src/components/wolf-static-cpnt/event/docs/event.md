# 依次做过功能开发文档

## 功能概述

用户实时行为 "依次做过"是FilterSingle组件中的一个特殊功能，允许用户添加多个事件序列，每个事件都可以独立配置事件选择器和过滤条件。

## 核心架构

### 数据结构设计

```javascript
// FilterModel.js 中的数据结构
todayDoEvents: [
  {
    eventInfo: {
      id: 331,
      eventType: 'BURIED_POINT_EVENT',
      displayName: '薪资到账',
      // ...其他事件信息
    },
    eventFilterProperty: {
      connector: 'AND',
      filters: [
        // 过滤条件数组
      ]
    }
  }
  // 更多事件...
]
```

### 核心文件结构

```
src/components/event/
├── FilterModel.js          # 数据模型，包含todayDoEvents管理方法
├── FilterSingle.vue        # 主组件，包含依次做过的UI和逻辑
├── FilterEventFieldSelect.vue  # 事件选择组件
└── 依次做过功能开发文档.md   # 本文档
```

## 关键实现要点

### 1. FilterModel.js 核心方法

```javascript
// 检查是否为依次做过模式
isTodayDoSeq() {
  return this.action === 'DO_SEQ'
}

// 添加新的依次做过事件
addTodayDoEvent() {
  if (!this.todayDoEvents) {
    this.todayDoEvents = []
  }
  this.todayDoEvents.push({
    eventInfo: {},
    eventFilterProperty: {
      connector: "AND",
      filters: []
    }
  })
}

// 更新指定索引的事件数据
updateTodayDoEvent(index, eventData) {
  if (this.todayDoEvents && this.todayDoEvents[index]) {
    this.todayDoEvents[index] = { ...this.todayDoEvents[index], ...eventData }
  }
}

// 删除指定索引的事件
removeTodayDoEvent(index) {
  if (this.todayDoEvents && this.todayDoEvents.length > 1) {
    this.todayDoEvents.splice(index, 1)
  }
}
```

### 2. FilterSingle.vue 界面实现

```vue
<!-- 依次做过模板结构 -->
<div v-if="isTodayDoSeq" style="display: flex !important; flex-direction: column; gap: 8px;">
  <div v-for="(event, index) in todayDoEvents" :key="index">
    <!-- 序号 + 事件选择器 + 删除按钮 -->
    <div style="display: flex; align-items: center;">
      <!-- 橙色序号 -->
      <div class="sequence-number">{{ index + 1 }}
</div>

      <!-- 事件选择器 -->
      <FilterEventFieldSelect
        :value="getTodayDoEventValue(index)"
        :on-change="() => {}"
      />

      <!-- 删除按钮 -->
      <a-icon type="close-circle" @click="removeTodayDoEvent(index)" />
    </div>

    <!-- 独立的过滤器 -->
    <BaseFilter
      :ref="`todayDoFilterRef_${index}`"
      :data-provider="getTodayDoEventDataProvider(index)"
      :value="event.eventFilterProperty"
      :on-change="onTodayDoEventFilterChange(index)"
    />

    <!-- 添加过滤条件按钮 -->
    <div class="Ctroller">
      <span @click="onAddTodayDoFilter(index)">
        <a-icon type="plus-circle" /> 条件
      </span>
    </div>
  </div>
</div>
```

## 关键问题与解决方案

### 问题1: FilterEventFieldSelect的changeProperty错误

**问题描述**:
```
Error in v-on handler: "TypeError: this.value.changeProperty is not a function"
```

**根本原因**:
- FilterEventFieldSelect组件内部会调用 `this.value.changeProperty()` 来更新数据
- 如果传递简单对象 `{ eventInfo: event.eventInfo }`，该对象没有changeProperty方法

**解决方案**: 创建模拟的value对象

```javascript
// FilterSingle.vue
getTodayDoEventValue(index) {
  const event = this.todayDoEvents[index]
  return {
    eventInfo: event.eventInfo,
    changeProperty: (newData) => {
      // 把onChange逻辑内置到changeProperty中
      this.value.updateTodayDoEvent(index, { eventInfo: newData.eventInfo })
      this.onChange(this.value)
    }
  }
}
```

**工作流程**:
```
用户选择事件
→ FilterEventFieldSelect.onEventFilterChange()
→ this.value.changeProperty() 调用我们提供的方法
→ 更新FilterModel中的todayDoEvents
→ 通知父组件数据变更
```

### 问题2: 为什么onChange可以是空函数？

**关键理解**: FilterEventFieldSelect的执行顺序
```javascript
// FilterEventFieldSelect.vue 的 onEventFilterChange 方法
onEventFilterChange(v) {
  // 1. 先调用changeProperty更新数据
  this.value.changeProperty({
    ...this.value,
    [eventInfoProperty]: newEventInfo
  })

  // 2. 再调用父组件传入的onChange
  this.onChange(this.value)
}
```

因为真正的数据更新在第1步的changeProperty中已经完成，所以第2步的onChange可以是空函数。

### 问题3: 数据验证

每个依次做过事件的过滤器都需要独立验证：

```javascript
// FilterSingle.vue
isValid() {
  // 验证依次做过事件的过滤器
  if (this.isTodayDoSeq && this.todayDoEvents.length > 0) {
    for (let i = 0; i < this.todayDoEvents.length; i++) {
      const filterRef = this.$refs[`todayDoFilterRef_${i}`]
      if (filterRef && filterRef[0] && !_.isEmpty(this.todayDoEvents[i].eventFilterProperty)) {
        const isValidTodayDoEvent = filterRef[0].isValid && filterRef[0].isValid(true)
        if (!isValidTodayDoEvent) {
          return false
        }
      }
    }
  }
  return true
}
```

## 数据流图

```
用户点击"后续行为"
→ houxuxingwei()
→ FilterModel.addTodayDoEvent()
→ 更新todayDoEvents数组
→ Vue响应式更新界面

用户选择事件
→ FilterEventFieldSelect.onEventFilterChange()
→ getTodayDoEventValue().changeProperty()
→ FilterModel.updateTodayDoEvent()
→ 通知父组件变更

用户添加过滤条件
→ onAddTodayDoFilter()
→ BaseFilter.addFilterGroup()
→ FilterModel.updateTodayDoEvent()
→ 更新对应事件的eventFilterProperty
```

## 维护要点

### 1. 关键依赖关系
- FilterEventFieldSelect **必须**接收有changeProperty方法的value对象
- BaseFilter的ref命名规则：`todayDoFilterRef_${index}`
- 数据更新必须通过FilterModel的方法，保持数据一致性

### 2. 扩展注意事项
- 添加新功能时，确保同时更新isValid方法
- 新的事件类型需要在getTodayDoEventDataProvider中正确设置eventId
- UI变更时注意保持序号显示和删除按钮的交互逻辑

### 3. 调试技巧
- 使用Vue DevTools查看todayDoEvents数据结构
- 检查ref是否正确创建：`this.$refs[`todayDoFilterRef_${index}`]`
- 验证FilterModel实例的方法是否正确调用

## 最佳实践

1. **数据管理集中化**: 所有todayDoEvents相关操作都通过FilterModel方法
2. **组件设计一致性**: 遵循FilterEventFieldSelect的原有设计模式
3. **错误处理**: 添加适当的边界检查，防止数组越界
4. **性能考虑**: 大量事件时考虑虚拟滚动优化

## 测试用例

```javascript
// 基本功能测试
1. 点击"后续行为"能添加新事件
2. 每个事件可以独立选择和配置
3. 删除事件功能正常
4. 添加过滤条件功能正常
5. 表单验证覆盖所有事件的过滤器

// 边界测试
1. 最大事件数量限制（isTodayMaxCount = 5）
2. 最后一个事件不能删除
3. 空数据状态处理
```

## 相关代码参考

- 参考 `FilterListGroup.vue` 了解多Filter管理模式
- 参考普通FilterSingle了解标准的事件选择器用法
- 查看FilterModel.js的其他方法了解数据管理模式
