<script>
import { SelectTime } from '@/components/wolf-static-cpnt'

export default {
  name: 'FilterEventSelectTime',
  components: {
    SelectTime,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    isActionCollection: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dateRange: this.value?.dateRange,
    }
  },
  computed: {},
  methods: {
    handleChange(v) {
      this.value.changeProperty({ ...this.value, dateRange: v })
      this.onChange(this.value)
    },
  },
}
</script>

<template>
  <SelectTime show-time :data="dateRange" :is-action-collection="isActionCollection" :on-change="handleChange" />
</template>

<style scoped>
</style>
