<script>
import FILTER_CONFIG from '../../config/FilterConfig'

export default {
  name: 'FilterOperator',
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    operatorOptions() {
      // const fieldType = this.getFieldType()
      const availableOperators = FILTER_CONFIG.typeOperator.INT
      return FILTER_CONFIG.operatorList.filter(op => availableOperators.includes(op.operator))
    },
  },
  methods: {
    getFieldType() {
      const { eventAggregateProperty } = this.value
      if (eventAggregateProperty?.propertyType === 'TIMES') {
        return 'INT'
      }
      return eventAggregateProperty?.property?.fieldType || 'INT'
    },

    handleChange(v) {
      this.value.changeOperator(v)
      this.onChange(this.value)
    },
  },
}
</script>

<template>
  <a-select
    :value="value.eventAggregateProperty?.operator || undefined"
    style="width: 100%"
    placeholder="操作符"
    @change="handleChange"
  >
    <a-select-option v-for="item in operatorOptions" :key="item.operator" :value="item.operator">
      {{ item.name }}
    </a-select-option>
  </a-select>
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
