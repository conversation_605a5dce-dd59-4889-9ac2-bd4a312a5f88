<script>
import FILTER_CONFIG from '../../config/FilterConfig'

export default {
  name: 'FilterEventFunction',
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    functionOptions() {
      const fieldType = this.value.eventAggregateProperty?.property?.fieldType || 'INT'
      return FILTER_CONFIG.CONDITIONFUN[fieldType] || []
    },
  },
  methods: {
    handleChange(v) {
      // eslint-disable-next-line vue/no-mutating-props
      this.value.eventAggregateProperty.fun = v
      this.onChange(this.value)
    },
  },
}
</script>

<template>
  <a-select
    :value="value.eventAggregateProperty?.fun || undefined"
    style="width: 100%"
    placeholder="计数函数"
    @change="handleChange"
  >
    <a-select-option v-for="item in functionOptions" :key="item.value" :value="item.value">
      {{ item.name }}
    </a-select-option>
  </a-select>
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
