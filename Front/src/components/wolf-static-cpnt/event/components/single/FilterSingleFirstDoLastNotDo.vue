<script>
import _ from 'lodash'
import BaseFilter from '../../../filter/Filter.vue'
import FILTER_CONFIG from '../../config/FilterConfig'
import { validationMixin } from '../../mixins/validationMixin'
import FilterModelUtil from '../../models/FilterModelUtil'
import { EVENT_CONSTANTS } from '../../utils/constants'
import { generateEventTimeDisplayText, generateLastEventTimeDisplayText } from '../../utils/utils'
import FilterEventAction from '../fields/FilterEventAction.vue'
import FilterEventFieldSelect from '../fields/FilterEventFieldSelect.vue'
import FilterTimeInput from '../fields/FilterTimeInput.vue'
import FilterSingleWrapper from '../filter/FilterSingleWrapper.vue'

export default {
  name: 'FilterSingleFirstDoLastNotDo',
  components: {
    BaseFilter,
    FilterSingleWrapper,
    FilterEventFieldSelect,
    FilterEventAction,
    FilterTimeInput,
  },
  mixins: [validationMixin],
  inject: {
    filterContext: 'filterContext',
    filterListGroup: {
      from: 'filterListGroup',
      default: () => null,
    },
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    onDelete: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      EVENT_ACTION: FILTER_CONFIG.EVENT_ACTION,
      _,
      MAX_FILTER_CONDITIONS: EVENT_CONSTANTS.MAX_FILTER_CONDITIONS,
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    mode() {
      return this.context.mode || 'edit'
    },
    dataProvider() {
      return this.context.dataProvider || {}
    },
    // 安全地获取完整的过滤组数据
    fullFilterListGroup() {
      return this.filterListGroup ? this.filterListGroup() : null
    },
    eventInfo() {
      return this.value.eventInfo || {}
    },
    lastEventInfo() {
      return this.value.lastEventInfo || {}
    },
    eventFilterProperty() {
      return this.value.eventFilterProperty || {}
    },
    lastEventFilterProperty() {
      return this.value.lastEventFilterProperty || {}
    },
    dataProviderComputed() {
      const pickPd = _.pick(this.dataProvider, 'getPropertyList')
      pickPd.eventId = this.eventInfo.id || 0
      return pickPd
    },
    lastDataProviderComputed() {
      const pickPd = _.pick(this.dataProvider, 'getPropertyList')
      pickPd.eventId = this.lastEventInfo.id || 0
      return pickPd
    },
    eventTimeDisplayText() {
      return generateEventTimeDisplayText(this.value, FILTER_CONFIG)
    },
    lastEventTimeDisplayText() {
      return generateLastEventTimeDisplayText(this.value, FILTER_CONFIG)
    },
    isFirstDoLastNotDo() {
      return this.value.isFirstDoLastNotDo()
    },
  },
  methods: {
    /**
     * 处理第一个事件过滤器变更
     * @param {object} data - 过滤器数据
     */
    onChangeFilter(data) {
      this.value.changePropertyValue(data)
      this.onChange(this.value)
    },

    /**
     * 处理最后事件过滤器变更
     * @param {object} data - 过滤器数据
     */
    onChangeLastFilter(data) {
      this.value.changeLastEventFilterProperty(data)
      this.onChange(this.value)
    },

    /**
     * 添加第一个事件过滤条件
     */
    onAddFilter() {
      if (this.$refs.filterRef && this.$refs.filterRef.addFilterGroup) {
        const filters = this.$refs.filterRef.addFilterGroup()
        this.value.changePropertyValue(filters)
        this.onChange(this.value)
      }
    },

    /**
     * 添加最后事件过滤条件
     */
    onAddLastFilter() {
      if (this.$refs.lastFilterRef && this.$refs.lastFilterRef.addFilterGroup) {
        const filters = this.$refs.lastFilterRef.addFilterGroup()
        this.value.changeLastEventFilterProperty(filters)
        this.onChange(this.value)
      }
    },

    /**
     * 处理 radio-group 的 change 事件
     */
    onPushDataChange(e) {
      this.handlePushDataChange(e.target.value)
    },

    /**
     * 处理 radio 的 click 事件（用于取消选择）
     */
    onRadioClick() {
      // 如果当前已经选中，则取消选择
      if (this.value.pushData === true) {
        this.handlePushDataChange(false)
      }
    },

    /**
     * 统一的 pushData 处理方法
     */
    handlePushDataChange(pushDataValue) {
      // 使用互斥方法设置pushData
      FilterModelUtil.setPushDataExclusive(this.fullFilterListGroup, this.value, pushDataValue)
      this.onChange(this.value)
    },
  },
}
</script>

<template>
  <li :class="`FilterSingle ${mode}`">
    <!-- 第一个事件行 -->
    <div
      style="display: flex"
      :style="{
        display: mode !== 'edit' && !value.valid().isValid ? 'none' : 'flex',
      }"
    >
      <!-- 实时行为最开始的时间 -->
      <div :class="`FilterField ${mode} ${validator?.firstAction && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper
          :value="eventTimeDisplayText"
          :use-take-place-width="true"
        >
          <FilterTimeInput :value="value" :on-change="onChange" type="first" />
        </FilterSingleWrapper>
      </div>

      <!-- 事件行为 -->
      <div :class="`FilterEventAction ${mode} ${validator?.action && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="EVENT_ACTION[value.action]" :use-take-place-width="true" style="width:auto">
          <FilterEventAction :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 事件选择 -->
      <div :class="`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="eventInfo.displayName" :use-take-place-width="true" style="min-width: 160px;">
          <FilterEventFieldSelect :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 控制器 -->
      <div v-if="mode === 'edit'" class="Ctroller">
        <a-tooltip v-if="value.validating && hasValidationError" placement="topRight" :title="validationMessage">
          <div style="margin-right: 5px; color: #fa7777;">
            <a-icon type="question-circle" />
          </div>
        </a-tooltip>
        <span
          class="handleAdd"
          :style="{
            display:
              $refs.filterRef && $refs.filterRef.getFilterCount && $refs.filterRef.getFilterCount() >= MAX_FILTER_CONDITIONS
                ? 'none'
                : 'inline-block',
          }"
          @click="onAddFilter"
        >
          <a-icon type="plus-circle" /> <span class="add-text">条件</span>
        </span>
        <a-radio-group :value="value.pushData" @change="onPushDataChange">
          <a-radio :value="true" @click="onRadioClick">
            <span class="checkboxTitle">推送数据</span>
          </a-radio>
        </a-radio-group>
        <a-icon type="close-circle" @click="onDelete" />
      </div>
    </div>

    <!-- 第一个事件的内部过滤器 -->
    <BaseFilter
      ref="filterRef"
      class="innerFilter !pl-48"
      :data-provider="dataProviderComputed"
      :value="eventFilterProperty"
      :on-change="onChangeFilter"
      :mode="mode"
      :hide-add="true"
      :hide-init="true"
      add-button-text="添加过滤条件"
    />

    <!-- 第二个事件行 (先做过, 后未做过) -->
    <div v-if="isFirstDoLastNotDo">
      <div style="display: flex">
        <div
          :class="`FilterField ${mode} ${validator?.lastTimeValue && validator?.lastTimeUnit && value.validating ? 'has-error' : ''}`"
          style="padding-left: 48px"
        >
          <FilterSingleWrapper
            :value="lastEventTimeDisplayText"
            :use-take-place-width="true"
            style="width:auto"
          >
            <FilterTimeInput :value="value" :on-change="onChange" type="last" />
          </FilterSingleWrapper>
        </div>

        <div :class="`FilterEventAction ${mode} ${validator?.action && value.validating ? 'has-error' : ''}`">
          <FilterSingleWrapper :value="EVENT_ACTION[value.lastAction]" :use-take-place-width="true" style="width:auto">
            <FilterEventAction :value="value" :on-change="onChange" type="last" />
          </FilterSingleWrapper>
        </div>
        <!-- 事件选择 -->
        <div :class="`FilterField ${mode} ${validator?.lastEventId && value.validating ? 'has-error' : ''}`">
          <FilterSingleWrapper :value="lastEventInfo.displayName" :use-take-place-width="true" style="width:auto">
            <FilterEventFieldSelect :value="value" :on-change="onChange" type="last" />
          </FilterSingleWrapper>
        </div>

        <!-- 控制器 -->
        <div v-if="mode === 'edit'" class="Ctroller" style="display:'block">
          <span
            class="handleAdd"
            :style="{
              display:
                $refs.lastFilterRef && $refs.lastFilterRef.getFilterCount && $refs.lastFilterRef.getFilterCount() >= MAX_FILTER_CONDITIONS
                  ? 'none'
                  : 'inline-block',
            }"
            @click="onAddLastFilter"
          >
            <a-icon type="plus-circle" /> <span class="add-text">条件</span>
          </span>
          <a-icon type="close-circle" @click="onDelete" />
        </div>
      </div>

      <!-- 最后事件的内部过滤器 -->
      <BaseFilter
        ref="lastFilterRef"
        class="innerFilter"
        :data-provider="lastDataProviderComputed"
        :value="lastEventFilterProperty"
        :on-change="onChangeLastFilter"
        :mode="mode"
        :hide-add="true"
        :hide-init="true"
        add-button-text="添加过滤条件"
      />
    </div>
  </li>
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
