<script>
import _ from 'lodash'
import FilterModelUtil from '../../models/FilterModelUtil'
import FilterGroup from './FilterGroup.vue'
import FilterSingle from './FilterSingle.vue'

export default {
  name: 'FilterListGroup',
  components: {
    FilterGroup,
    FilterSingle,
  },
  inject: ['filterContext'],
  provide() {
    return {
      filterListGroup: () => this.value || {},
    }
  },
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    context() {
      return this.filterContext()
    },
    logProvider() {
      return (
        this.context.logProvider || {
          getLogger: () => ({ debug: console.warn }),
        }
      )
    },
    mode() {
      return this.context.mode || 'edit'
    },
    isActionCollection() {
      return this.context.isActionCollection || false
    },
    // 检查是否有有效的 value，与 React 版本保持一致
    hasValue() {
      // console.warn("hasValue", this.value);
      return !!(this.value && this.value.filters)
    },
    filterListCount() {
      return this.value.filters ? this.value.filters.length : 0
    },
  },
  mounted() {
    const log = this.logProvider.getLogger('FilterListGroup')
    // 调试日志
    if (this.value) {
      log.debug('connector', this.value.connector)
      log.debug('filters', this.value.filters)
    }
  },
  methods: {
    onChangeFilter(_filterList, _index) {
      return (_newValue) => {
        // console.warn("FilterListGroup onChangeFilter 接收到:", newValue);
        // 直接调用 onChange，因为 FilterSingle 已经修改了 filter 对象
        this.onChange(this.value)
      }
    },

    onAddFilter(filterList, index) {
      return () => {
        // 获取第一个过滤器的firstAction作为新过滤器的默认值
        const firstFilterFirstAction = FilterModelUtil.getFirstFilterFirstAction(this.value)
        filterList.filters = [
          ...filterList.filters.slice(0, index + 1),
          FilterModelUtil.createFilter(this.isActionCollection, firstFilterFirstAction),
          ...filterList.filters.slice(index + 1),
        ]
        this.onChange(this.value)
      }
    },

    onDeleteFilter(filterList, index) {
      return () => {
        filterList.filters.splice(index, 1)
        FilterModelUtil.deleteEmptyFilterList(this.value)
        this.onChange(this.value)
      }
    },

    getFilterGroupShouldRender(filterList) {
      if (!filterList || !filterList.filters)
        return false
      const filterCount
        = this.mode === 'detail' ? FilterModelUtil.getValidFilterListCount(filterList) : filterList.filters.length
      return filterCount > 0
    },

    getFilterCount(filterList) {
      return this.mode === 'detail' ? FilterModelUtil.getValidFilterListCount(filterList) : filterList.filters.length
    },

    onChangeConnector(filter) {
      return (v) => {
        filter.connector = v
        this.onChange(this.value)
      }
    },

    // 暴露给父组件的方法
    isValid(_flag) {
      const refArr = []
      // 收集所有 FilterSingle 的 ref
      this.value.filters.forEach((filterList, index) => {
        filterList.filters.forEach((filter, i) => {
          const refKey = `filterSingle_${index}_${i}`
          const ref = this.$refs[refKey]
          if (ref && ref[0]) {
            refArr.push(ref[0])
          }
        })
      })

      const validResults = _.without(refArr, null, undefined, false)
      if (_.isEmpty(validResults))
        return true

      const results = validResults.map((item) => {
        const result = item.isValid ? item.isValid() : true
        return result
      })

      const finalResult = results.reduce((a, b) => a && b, true)

      return finalResult
    },
  },
}
</script>

<template>
  <div v-if="hasValue" class="FilterGroupListPannel1">
    <FilterGroup
      v-if="filterListCount > 0"
      :connector="value.connector"
      :on-change-connector="onChangeConnector(value)"
      :filter-count="filterListCount"
      inner=""
    >
      <template v-for="(filterList, index) in value.filters">
        <FilterGroup
          v-if="getFilterGroupShouldRender(filterList)"
          :key="index"
          :connector="filterList.connector"
          :on-change-connector="onChangeConnector(filterList)"
          :filter-count="getFilterCount(filterList)"
          inner="inner"
        >
          <FilterSingle
            v-for="(filter, i) in filterList.filters"
            :key="`${filter.key}`"
            :ref="`filterSingle_${index}_${i}`"
            :value="filter"
            :on-change="onChangeFilter(filterList, i)"
            :on-add="onAddFilter(filterList, i)"
            :on-delete="onDeleteFilter(filterList, i)"
          />
        </FilterGroup>
      </template>
    </FilterGroup>
  </div>
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
