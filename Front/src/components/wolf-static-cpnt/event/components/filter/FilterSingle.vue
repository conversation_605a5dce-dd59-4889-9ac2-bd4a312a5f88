<script>
import { FIRST_ACTIONS } from '../../utils/constants'
import FilterSingleDone from '../single/FilterSingleDone.vue'
import FilterSingleDoSeq from '../single/FilterSingleDoSeq.vue'
import FilterSingleFirstDoLastNotDo from '../single/FilterSingleFirstDoLastNotDo.vue'

export default {
  name: 'FilterSingle',
  components: {
    FilterSingleDone,
    FilterSingleFirstDoLastNotDo,
    FilterSingleDoSeq,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    onDelete: {
      type: Function,
      default: () => {},
    },
    onAdd: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    // 判断是否为"先做过, 后未做过"
    isFirstDoLastNotDo() {
      return this.value.isFirstDoLastNotDo()
    },
    // 判断是否是依次做过
    isTodayDo() {
      return this.value.isTodayDo()
    },
    // 获取当前模式
    currentMode() {
      const firstAction = this.value.firstAction

      if (firstAction === FIRST_ACTIONS.FIRST_DO_LAST_NOT_DO) {
        return 'FIRST_DO_LAST_NOT_DO'
      }
      else if (firstAction === FIRST_ACTIONS.DO_SEQ) {
        return 'DO_SEQ'
      }
      else {
        // 默认为DONE模式
        return 'DONE'
      }
    },
    // 动态选择组件
    currentComponent() {
      switch (this.currentMode) {
        case 'FIRST_DO_LAST_NOT_DO':
          return 'FilterSingleFirstDoLastNotDo'
        case 'DO_SEQ':
          return 'FilterSingleDoSeq'
        case 'DONE':
        default:
          return 'FilterSingleDone'
      }
    },
  },
  methods: {
    /**
     * 验证组件有效性
     * 委托给当前激活的子组件
     * @returns {boolean} 是否有效
     */
    isValid() {
      if (this.$refs.currentModeComponent && this.$refs.currentModeComponent.isValid) {
        return this.$refs.currentModeComponent.isValid()
      }
      return true
    },
  },
}
</script>

<template>
  <component
    :is="currentComponent"
    ref="currentModeComponent"
    :value="value"
    :on-change="onChange"
    :on-delete="onDelete"
    :on-add="onAdd"
  />
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
